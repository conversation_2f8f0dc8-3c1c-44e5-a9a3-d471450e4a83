# vite2

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm run dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm run lint
```

现在我有以下需求：

1. 我要开发"排查案例库"菜单，该菜单是知识库管理的二级菜单；
2. 主要做增删改查功能，接下来我会把接口全部写出来：
   2-1. 获取 排查案例库 列表接口：/CheckCaseInfo/CheckCaseInfoList
   入参是：
   {
   "currentPage": 1,
   "pageSize": 2,
   "checkCaseName": "", //案例名称
   "applicableRegion": "", //适用地区（下拉，适用地区字典）
   "investigationStartDate": "", //排查时间（开始，区间日期选择框），yyyy-MM-dd
   "investigationEndDate": "", //排查时间（结束，区间日期选择框），yyyy-MM-dd
   "investigationTarget": "", //排查对象
   "source": "", //排查来源（下拉，字典pcly）
   "investigationType": "", //排查类型（下拉，字典pclx）
   "problemType": [
   ""
   ], //排查问题类型（下拉多选，字典pcwtlx）
   "dataStatus": 1 //案例状态（下拉，状态下拉）
   }
   出参是：
   {
   "code": 200,
   "msg": "success",
   "data":
   "list": [
   {
   "checkCaseId": "PC20250910134596",
   "checkCaseName": "上级交办", //案例名称
   "applicableRegion": [ "310104" ],
   "investigationStartDate": "2025-09-10",
   "investigationEndDate": "2025-09-21",
   "investigationTarget": "2222", //排查对象
   "source": [ "2" ],
   "investigationType": [ "3" ],
   "policyIds": [ "ZC20250905125807" ],
   "investigationMethod": "qqq",
   "investigationScope": "333",
   "caseSummary": "444",
   "inputUser": null,
   "inputTime": "2025-09-10 17:13:21",
   "updateUser": null,
   "updateTime": "2025-09-10 17:13:21", //更新时间
   "dataStatus": 1,
   "remarks": "555",
   "problemList": [
   {
   "problemId": "779f9317-601f-4c73-91d3-18a8f664cb98",
   "checkCaseId": "PC20250910134596",
   "problemType": [ "1" ],
   "mainProblemDescription": "111", //主要问题描述
   "regulationClause": "222", //依据法规条款
   "measures": "333", //处理措施
   "rectificationDeadline": "2024-04-10", //整改时限
   "rectificationResult": 1,
   "inputUser": null,
   "inputTime": "2025-09-10 17:17:32",
   "updateUser": null,
   "updateTime": "2025-09-10 17:17:32",
   "remarks": "7777",
   "problemTypeName": "污染源自动监控类", //排查问题类型
   "rectificationResultName": "已完成整改", //整改结果
   "rowStatus": 1
   }
   ], //问题详情数组
   "applicableRegionName": "徐汇区", //排查地区
   "sourceName": "上级交办", //排查来源
   "investigationTypeName": "四大专项", //排查类型
   "policyIdsName": "测试长三角",
   "dataStatusName": "正常", //案例状态
   "problemTypeName": "污染源自动监控类", //排查问题类型
   "fileList": [
   {
   "fileId": "5038b2aa-c1be-41af-8b27-a215589fd57e",
   "fileName": "yxkj.png",
   "filePath": null,
   "tableName": null,
   "tableId": null,
   "fileType": null,
   "isActive": null,
   "url": "File/getFile?fileName=79d03bf2-5d9c-4437-b579-3c37ec6847c2.png&type=checkCaseInfo"
   }
   ] //附件
   }
   ],
   "pagination": {
   "currentPage": 1,
   "pageSize": 2,
   "tableProp": "",
   "tableOrder": "",
   "total": 1
   }
   }

   2-2. 新增排查案例库接口：/CheckCaseInfo/saveCheckCaseInfo
   入参是：
   {
   "info": {
   "checkCaseName": "上级交办", //案例名称
   "applicableRegion": [
   "310104"
   ], //实施地区（下拉，适用地区字典，多选）
   "investigationStartDate": "2025-09-10", //排查时间（开始，区间日期选择框），yyyy-MM-dd
   "investigationEndDate": "2025-09-21", //排查时间（结束，区间日期选择框），yyyy-MM-dd
   "investigationTarget": "2222", //排查对象
   "source": [
   "2"
   ], //排查来源（下拉，字典pcly）
   "investigationType": [
   "3"
   ], //排查类型（下拉，字典pclx）
   "policyIds": [
   "ZC20250905125807"
   ], //关联政策（之前接口）
   "investigationMethod": "qqq", //排查方法
   "investigationScope": "333", //排查范围
   "caseSummary": "444", //案例分析总结
   "dataStatus": 1, //数据状态（暂存-2，提交-3）
   "remarks": "555", //备注
   "problemList": [
   {
   "problemId": "", //新增不传
   "checkCaseId": "", //新增不传
   "problemType": [
   "1"
   ], //排查问题类型（下拉多选，字典pcwtlx）
   "mainProblemDescription": "111", //主要问题描述
   "regulationClause": "222", //依据法规条款
   "measures": "333", //处理措施
   "rectificationDeadline": "2024-04-10", //整改时限
   "rectificationResult": "1", //整改结果（下拉，整改结果下拉）
   "remarks": "7777" //备注
   }
   ] //问题详情表
   },
   "fileList": {
   "addFileIds": [
   "",
   ""
   ] //新增的文件id（文件上传接口type=caseInfo返回值）
   } //附件
   }

   2-3. 编辑排查案例库接口：/CheckCaseInfo/updateCheckCaseInfo
   2-4. 删除核算系数库接口：/CheckCaseInfo/deleteCheckCaseInfo
   入参是：
   {
   "info": {
   "checkCaseId": "ZN20250910123309"
   }
   }
   2-5. 获取状态下拉接口：/CheckCaseInfo/getDataStatusList
   {
   "code": 200,
   "msg": "success",
   "data": [
   {
   "value": 2,
   "label": "暂存",
   },
   {
   "value": 3,
   "label": "待审核",
   },
   {
   "value": 1,
   "label": "正常",
   },
   {
   "value": -1,
   "label": "失效",
   }
   ]}
   2-6. 审核接口：/CheckCaseInfo/approvedCheckCaseInfo
   {
   "info": {
   "checkCaseId": "PC20250910134596", //数据id
   "dataStatus": 1 //数据状态（通过（1），不通过（-3））
   }
   }
   2-7.获取用途的字典查询接口：使用全局封装的方法 fetchDictDataByTypes
   "source": "", //排查来源（下拉，字典pcly）
   "investigationType": "", //排查类型（下拉，字典pclx）
   "problemType": [
   ""
   ], //排查问题类型（下拉多选，字典pcwtlx）
   2-8.获取地区数据使用全局的 fetchDictDataByTypes

3. 保存为草稿的时候dataStatus传2，提交的时候传3。
4. 解决所有TS报错。
5. 删除项目里面多余的逻辑部分。
6. 接口写在src/api/knowledge.ts 文件夹里面
