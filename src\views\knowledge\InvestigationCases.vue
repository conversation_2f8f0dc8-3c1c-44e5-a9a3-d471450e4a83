<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>排查案例库</span>
          <el-button type="primary">添加案例</el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="title" label="案例标题" width="300" />
        <el-table-column prop="type" label="排查类型" width="120" />
        <el-table-column prop="source" label="污染源" width="150" />
        <el-table-column prop="result" label="排查结果" width="200" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const tableData = ref([
  {
    id: 1,
    title: '某化工企业异味排查案例',
    type: '异味排查',
    source: '化工企业',
    result: '发现废气处理设施故障',
    createTime: '2024-01-22'
  },
  {
    id: 2,
    title: '工业园区PM2.5超标排查',
    type: '超标排查',
    source: '工业园区',
    result: '确定主要污染源为燃煤锅炉',
    createTime: '2024-01-20'
  }
])

const handleView = (row: any) => {
  console.log('查看案例:', row)
}

const handleEdit = (row: any) => {
  console.log('编辑案例:', row)
}

const handleDelete = (row: any) => {
  console.log('删除案例:', row)
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
