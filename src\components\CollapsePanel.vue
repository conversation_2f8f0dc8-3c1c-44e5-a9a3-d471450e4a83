<template>
  <div :class="{ isExpended: isExpended, child: child }" class="panel-wrap">
    <div
      v-if="showHeader"
      :class="{ borderBottom: isExpended || defaultMaxHeight != '0' }"
      class="panel-hd clearfix"
    >
      <div class="title fl">
        {{ title }}
        <slot name="panel-title" />
      </div>
      <div class="fr operate">
        <div class="buttons">
          <slot name="panel-button" />
        </div>
        <div v-if="showToggleBtn" class="toggle" @click="change">
          <span v-if="showText">
            {{ isExpended ? '收起' : '展开' }}
          </span>
          <el-icon class="arrow-icon" :class="{ 'is-expanded': isExpended }">
            <ArrowDown />
          </el-icon>
        </div>
      </div>
    </div>
    <div
      :class="{ opend: isExpended, autoHeight: autoHeight }"
      :style="{
        maxHeight: isExpended ? computedMaxHeight : defaultMaxHeight,
        paddingBottom: showToggleBtn && !isExpended && defaultMaxHeight === '0' ? 0 : '',
      }"
      class="panel-bd"
    >
      <div :class="{ [panelMainClass]: true, 'panel-main-folder': !isExpended }">
        <slot name="panel-main" />
      </div>
      <slot name="panel-bottom" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { throttle } from 'lodash-es'

// 定义组件属性
interface Props {
  showHeader?: boolean
  title?: string
  defaultMaxHeight?: string
  defaultMaxLine?: number
  maxHeight?: string
  showToggleBtn?: boolean
  autoHeight?: boolean
  expended?: boolean
  showText?: boolean
  child?: boolean
}

// 定义事件
interface Emits {
  (e: 'change', value: boolean): void
  (e: 'clickFn', value: { isShow: boolean }): void
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  title: '',
  defaultMaxHeight: '80px',
  defaultMaxLine: 0,
  maxHeight: '2400px',
  showToggleBtn: false,
  autoHeight: false,
  expended: false,
  showText: true,
  child: false,
})

const emit = defineEmits<Emits>()

// 响应式数据
const isExpended = ref(false)
const panelMainClass = ref(`t_${new Date().getTime()}`)

// 计算属性
const computedMaxHeight = computed(() => {
  // 加1是给头部固定区域的,同时不传值兼容旧的操作
  return props.defaultMaxLine ? `${(props.defaultMaxLine + 1) * 88}px` : props.maxHeight
})

// 滚动到顶部的节流函数
const scrollTopPanelMainZero = () => {
  const className = panelMainClass.value
  const dom = document.querySelector(`.${className}`)
  if (dom && dom.parentElement) {
    dom.parentElement.scrollTop = 0
  }
}

const fixLayoutFunc = throttle(scrollTopPanelMainZero, 100)

// 监听外部传入的expended属性
watch(
  () => props.expended,
  (val) => {
    isExpended.value = val
  },
  { immediate: true },
)

// 切换展开/收起状态
const change = () => {
  isExpended.value = !isExpended.value
  emit('change', isExpended.value)
  emit('clickFn', { isShow: isExpended.value })

  // 状态改变后重置滚动位置
  fixLayoutFunc()
}

// 暴露方法给父组件
defineExpose({
  toggle: change,
  isExpended: computed(() => isExpended.value),
})
</script>

<style scoped>
.panel-wrap {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.panel-wrap.child {
  box-shadow: none;
  border: 1px solid #e4e7ed;
}

.panel-hd {
  padding: 8px 16px;
  background: #fafafa;
  border-bottom: 1px solid transparent;
  position: relative;
}

.panel-hd.borderBottom {
  border-bottom-color: #e4e7ed;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.operate {
  display: flex;
  align-items: center;
  gap: 12px;
}

.buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #409eff;
  font-size: 14px;
  user-select: none;
  transition: color 0.3s;
}

.toggle:hover {
  color: #66b1ff;
}

.toggle span {
  margin-right: 4px;
}

.arrow-icon {
  transition: transform 0.3s;
  font-size: 14px;
}

.arrow-icon.is-expanded {
  transform: rotate(180deg);
}

.panel-bd {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  position: relative;
}

.panel-bd.autoHeight {
  max-height: none !important;
}

.panel-bd.opend {
  overflow: visible;
}

.panel-main-folder {
  position: relative;
}

/* 当收起状态时，添加渐变遮罩效果 */
.panel-main-folder::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.8));
  pointer-events: none;
}

.panel-wrap.isExpended .panel-main-folder::after {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-hd {
    padding: 12px 16px;
  }

  .operate {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }

  .title {
    font-size: 14px;
  }
}
</style>
