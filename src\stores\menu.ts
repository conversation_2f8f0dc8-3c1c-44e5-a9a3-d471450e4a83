import { ref } from 'vue'
import { defineStore } from 'pinia'

export interface MenuItem {
  title: string
  index: string
  route?: string
  children: MenuItem[]
  categories?: CategoryItem[]
}

export interface CategoryItem {
  title: string
  index: string
  route: string
}

export const useMenuStore = defineStore('menu', () => {
  const menuList = ref<MenuItem[]>([
    {
      title: '首页',
      index: '1',
      route: '/',
      children: [],
    },
    {
      title: '知识库管理',
      index: '2',
      children: [
        {
          title: '政策库',
          index: '2-1',
          route: '/knowledge/policy-list',
          children: [],
        },
        {
          title: '优秀管控案例库',
          index: '2-2',
          route: '/knowledge/excellent-cases',
          children: [],
          // categories: [
          //   {
          //     title: '工业治理案例',
          //     index: '2-1-1',
          //     route: '/knowledge/excellent-cases/industrial',
          //   },
          //   { title: '城市治理案例', index: '2-1-2', route: '/knowledge/excellent-cases/urban' },
          //   { title: '农村治理案例', index: '2-1-3', route: '/knowledge/excellent-cases/rural' },
          // ],
        },
        {
          title: '核算系数库',
          index: '2-3',
          route: '/knowledge/guideline-info',
          children: [],
        },
        // {
        //   title: '总量减排核算指南库',
        //   index: '2-2',
        //   route: '/knowledge/reduction-guide',
        //   children: [],
        // },
        // {
        //   title: '排查案例库',
        //   index: '2-3',
        //   route: '/knowledge/investigation-cases',
        //   children: [],
        // },
        // {
        //   title: '特征组分来源库',
        //   index: '2-4',
        //   route: '/knowledge/component-sources',
        //   children: [],
        // },
      ],
    },
    {
      title: '空气质量数据可视化',
      index: '3',
      children: [
        {
          title: '空间分布展示',
          index: '3-1',
          route: '/air-quality/spatial-distribution',
          children: [],
        },
        {
          title: '时间序列分析',
          index: '3-2',
          route: '/air-quality/time-series',
          children: [],
        },
        {
          title: '区域排名展示',
          index: '3-3',
          route: '/air-quality/regional-ranking',
          children: [],
        },
        {
          title: '同步环比分析',
          index: '3-4',
          route: '/air-quality/comparison-analysis',
          children: [],
        },
      ],
    },
    {
      title: '污染源可视化',
      index: '4',
      children: [
        {
          title: '工业源',
          index: '4-1',
          route: '/pollution-source/industrial',
          children: [],
          categories: [
            { title: '钢铁行业', index: '4-1-1', route: '/pollution-source/industrial/steel' },
            { title: '化工行业', index: '4-1-2', route: '/pollution-source/industrial/chemical' },
            { title: '电力行业', index: '4-1-3', route: '/pollution-source/industrial/power' },
          ],
        },
        {
          title: '企一档',
          index: '4-2',
          route: '/pollution-source/enterprise-profile',
          children: [],
        },
        {
          title: '一点一策',
          index: '4-3',
          route: '/pollution-source/point-strategy',
          children: [],
        },
      ],
    },
    {
      title: '污染成因分析',
      index: '5',
      children: [
        {
          title: '高值污染因子',
          index: '5-1',
          route: '/pollution-analysis/high-value-factors',
          children: [],
        },
        {
          title: '高值时段分析',
          index: '5-2',
          route: '/pollution-analysis/high-value-periods',
          children: [],
        },
        {
          title: '高值区域定位',
          index: '5-3',
          route: '/pollution-analysis/high-value-regions',
          children: [],
        },
        {
          title: '气象关联分析',
          index: '5-4',
          route: '/pollution-analysis/meteorological-correlation',
          children: [],
        },
      ],
    },
  ])

  // 根据路由查找菜单项
  const findMenuByRoute = (route: string): MenuItem | null => {
    for (const menu of menuList.value) {
      if (menu.route === route) return menu
      for (const child of menu.children) {
        if (child.route === route) return child
        if (child.categories) {
          for (const category of child.categories) {
            if (category.route === route) return child
          }
        }
      }
    }
    return null
  }

  // 根据路由查找分类项
  const findCategoryByRoute = (route: string): CategoryItem | null => {
    for (const menu of menuList.value) {
      for (const child of menu.children) {
        if (child.categories) {
          for (const category of child.categories) {
            if (category.route === route) return category
          }
        }
      }
    }
    return null
  }

  return {
    menuList,
    findMenuByRoute,
    findCategoryByRoute,
  }
})
